# 超级Agent - 智能分析与知识库系统

## 项目概述

**超级Agent** 是一个集成了数据分析、可视化、网络搜索、本地知识库RAG等功能的智能代理系统。它结合了混合分析能力和本地知识库功能，形成一个功能强大的超级智能助手。

## 核心功能

### 🧠 智能代理核心
- **LangGraph工作流引擎**: 基于状态图的任务执行流程
- **多模型支持**: 支持OpenAI、DeepSeek、Gemini等多种LLM
- **任务规划器**: 智能分解复杂任务为可执行步骤
- **执行引擎**: 协调各个功能模块的执行

### 📚 本地知识库RAG系统
- **FAISS向量数据库**: 高性能向量存储和检索
- **多格式文档支持**: PDF、Word、Excel、TXT、Markdown等
- **智能分块**: 自动文档分块和向量化
- **语义检索**: 基于语义相似度的知识检索
- **知识库管理**: 文档上传、更新、删除、索引重建

### 🌐 网络信息获取
- **SearxNG搜索引擎**: 聚合多个搜索引擎结果
- **智能网页解析**: 提取关键信息和结构化数据
- **图片搜索**: 获取相关图片和描述
- **实时信息**: 获取最新的网络信息和数据

### 📊 数据分析与可视化
- **统计分析**: 描述性统计、相关性分析、回归分析
- **数据清洗**: 自动处理缺失值、异常值、重复数据
- **多种图表**: 散点图、柱状图、热力图、箱线图等
- **交互式可视化**: 基于Plotly的动态图表
- **报告生成**: 自动生成包含图表的分析报告

### 🎨 前端界面系统
- **知识库管理**: 文档上传、预览、管理界面
- **任务配置**: 灵活的任务配置和参数设置
- **实时监控**: 任务执行进度和状态显示
- **结果展示**: 报告预览、文件管理、下载功能
- **系统管理**: 配置管理、日志查看、性能监控

## 技术架构

### 核心技术栈
```
Frontend: Gradio + HTML/CSS/JavaScript
Backend: Python + FastAPI
Agent Framework: LangGraph + LangChain
Vector Database: FAISS + HuggingFace Embeddings
Search Engine: SearxNG + Docker
Data Processing: Pandas + NumPy + SciPy
Visualization: Matplotlib + Seaborn + Plotly
File Processing: PyPDF2 + python-docx + openpyxl
```

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd super_agent

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
cp config/.env.example config/.env
# 编辑 .env 文件，配置API密钥和其他参数
```

### 3. 启动服务
```bash
# 启动SearxNG搜索引擎
docker-compose -f docker/docker-compose.yml up -d

# 启动后端API服务
python backend/main.py

# 启动前端界面
python frontend/app.py
```

### 4. 访问系统
- 前端界面: http://localhost:7860
- API文档: http://localhost:8000/docs
- SearxNG搜索: http://localhost:8080

## 项目结构

```
super_agent/
├── agent/                  # 智能代理核心
│   ├── core/              # 核心引擎
│   ├── prompts/           # 提示词模板
│   └── tools/             # 工具集合
├── analysis/              # 数据分析模块
│   └── processors/        # 数据处理器
├── backend/               # 后端API服务
│   ├── api/               # API路由
│   └── services/          # 业务服务
├── config/                # 配置文件
├── data/                  # 数据存储
│   ├── cache/             # 缓存数据
│   ├── knowledge_base/    # 知识库文件
│   ├── outputs/           # 输出结果
│   └── uploads/           # 上传文件
├── docker/                # Docker配置
├── frontend/              # 前端界面
│   ├── components/        # UI组件
│   └── static/            # 静态资源
├── knowledge/             # 知识库模块
│   └── models/            # 向量模型
├── search/                # 搜索模块
│   └── engines/           # 搜索引擎
├── utils/                 # 工具函数
└── tests/                 # 测试文件
```

## 使用示例

### 数据分析任务
```python
from super_agent import SuperAgent

agent = SuperAgent()
result = agent.analyze_data(
    file_path="data/student_performance.csv",
    analysis_type="correlation",
    generate_report=True
)
```

### 知识库查询
```python
# 上传文档到知识库
agent.knowledge_base.upload_document("path/to/document.pdf")

# 查询知识库
result = agent.knowledge_base.search("机器学习算法")
```

### 混合分析任务
```python
# 结合本地知识库和网络搜索的分析
result = agent.hybrid_analysis(
    query="人工智能发展趋势",
    use_knowledge_base=True,
    use_web_search=True,
    generate_visualization=True
)
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
- 文档: [Documentation]
