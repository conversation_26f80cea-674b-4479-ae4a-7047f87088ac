"""
超级Agent配置管理模块
"""

import os
from pathlib import Path
from typing import List, Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Settings(BaseSettings):
    """系统配置类"""
    
    # ================================
    # 基础配置
    # ================================
    
    # 项目根目录
    PROJECT_ROOT: Path = Path(__file__).parent.parent
    
    # 应用信息
    APP_NAME: str = "超级Agent"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "智能分析与知识库系统"
    
    # 调试模式
    DEBUG: bool = Field(default=False, env="DEBUG")
    DEVELOPMENT: bool = Field(default=False, env="DEVELOPMENT")
    
    # ================================
    # LLM模型配置
    # ================================
    
    # OpenAI配置
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    OPENAI_BASE_URL: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    OPENAI_MODEL: str = Field(default="gpt-4-turbo-preview", env="OPENAI_MODEL")
    
    # DeepSeek配置
    DEEPSEEK_API_KEY: Optional[str] = Field(default=None, env="DEEPSEEK_API_KEY")
    DEEPSEEK_BASE_URL: str = Field(default="https://api.deepseek.com/v1", env="DEEPSEEK_BASE_URL")
    DEEPSEEK_MODEL: str = Field(default="deepseek-chat", env="DEEPSEEK_MODEL")
    
    # Gemini配置
    GOOGLE_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    GEMINI_MODEL: str = Field(default="gemini-pro", env="GEMINI_MODEL")
    
    # ================================
    # 向量数据库配置
    # ================================
    
    # FAISS配置
    FAISS_INDEX_PATH: str = Field(default="data/knowledge_base/faiss_index", env="FAISS_INDEX_PATH")
    EMBEDDING_MODEL_PATH: str = Field(default="models/bge-large-zh-v1.5", env="EMBEDDING_MODEL_PATH")
    EMBEDDING_DEVICE: str = Field(default="cpu", env="EMBEDDING_DEVICE")
    CHUNK_SIZE: int = Field(default=1000, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=200, env="CHUNK_OVERLAP")
    
    # ================================
    # 搜索引擎配置
    # ================================
    
    # SearxNG配置
    SEARXNG_URL: str = Field(default="http://localhost:8080", env="SEARXNG_URL")
    SEARXNG_LANGUAGE: str = Field(default="zh-CN", env="SEARXNG_LANGUAGE")
    SEARXNG_SAFESEARCH: int = Field(default=0, env="SEARXNG_SAFESEARCH")
    SEARXNG_CATEGORIES: str = Field(default="general", env="SEARXNG_CATEGORIES")
    
    # ================================
    # 数据库配置
    # ================================
    
    # SQLite配置
    DATABASE_URL: str = Field(default="sqlite:///data/super_agent.db", env="DATABASE_URL")
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # ================================
    # 服务配置
    # ================================
    
    # 后端API配置
    BACKEND_HOST: str = Field(default="0.0.0.0", env="BACKEND_HOST")
    BACKEND_PORT: int = Field(default=8000, env="BACKEND_PORT")
    BACKEND_WORKERS: int = Field(default=4, env="BACKEND_WORKERS")
    
    # 前端配置
    FRONTEND_HOST: str = Field(default="0.0.0.0", env="FRONTEND_HOST")
    FRONTEND_PORT: int = Field(default=7860, env="FRONTEND_PORT")
    FRONTEND_SHARE: bool = Field(default=False, env="FRONTEND_SHARE")
    
    # ================================
    # 文件存储配置
    # ================================
    
    # 上传文件配置
    UPLOAD_DIR: str = Field(default="data/uploads", env="UPLOAD_DIR")
    MAX_FILE_SIZE: str = Field(default="100MB", env="MAX_FILE_SIZE")
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=["pdf", "docx", "doc", "txt", "md", "csv", "xlsx", "xls"],
        env="ALLOWED_EXTENSIONS"
    )
    
    # 输出文件配置
    OUTPUT_DIR: str = Field(default="data/outputs", env="OUTPUT_DIR")
    CACHE_DIR: str = Field(default="data/cache", env="CACHE_DIR")
    
    # ================================
    # 安全配置
    # ================================
    
    # JWT配置
    JWT_SECRET_KEY: str = Field(default="your_jwt_secret_key_here", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_EXPIRE_MINUTES: int = Field(default=1440, env="JWT_EXPIRE_MINUTES")
    
    # 跨域配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:7860"],
        env="CORS_ORIGINS"
    )
    
    # ================================
    # 日志配置
    # ================================
    
    # 日志级别
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="logs/super_agent.log", env="LOG_FILE")
    LOG_MAX_SIZE: str = Field(default="10MB", env="LOG_MAX_SIZE")
    LOG_BACKUP_COUNT: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    # ================================
    # 性能配置
    # ================================
    
    # 并发配置
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    ASYNC_TIMEOUT: int = Field(default=300, env="ASYNC_TIMEOUT")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, env="CACHE_TTL")
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # ================================
    # 监控配置
    # ================================
    
    # Prometheus配置
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")
    METRICS_ENABLED: bool = Field(default=True, env="METRICS_ENABLED")
    
    # 健康检查
    HEALTH_CHECK_INTERVAL: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def get_absolute_path(self, relative_path: str) -> Path:
        """获取相对于项目根目录的绝对路径"""
        return self.PROJECT_ROOT / relative_path
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.UPLOAD_DIR,
            self.OUTPUT_DIR,
            self.CACHE_DIR,
            "logs",
            "data/knowledge_base",
            "models"
        ]
        
        for directory in directories:
            path = self.get_absolute_path(directory)
            path.mkdir(parents=True, exist_ok=True)

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
settings.ensure_directories()
