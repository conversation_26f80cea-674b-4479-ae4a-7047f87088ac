# 超级Agent项目依赖包

# 核心框架
langchain>=0.2.0
langgraph>=0.1.0
langchain-openai>=0.1.0
langchain-community>=0.2.0
langchain-core>=0.2.0

# MCP协议支持
trio>=0.25.1
mcp>=0.2.0

# 向量数据库和嵌入模型
faiss-cpu>=1.7.4
sentence-transformers>=2.2.2
transformers>=4.30.0
torch>=2.0.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0
openpyxl>=3.0.0
xlrd>=2.0.1
python-docx>=0.8.11
PyPDF2>=3.0.1

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
kaleido>=0.2.1

# Web框架
fastapi>=0.100.0
uvicorn>=0.23.0
gradio>=4.0.0

# 图像处理
Pillow>=9.0.0

# 环境变量管理
python-dotenv>=0.19.0

# HTTP请求
requests>=2.28.0
httpx>=0.24.0
aiohttp>=3.8.0

# 日志处理
loguru>=0.6.0

# 文件处理
pathlib2>=2.3.0
chardet>=5.0.0

# 数据验证
pydantic>=2.0.0

# 异步支持
asyncio>=3.4.3
aiofiles>=23.0.0

# 科学计算
scipy>=1.9.0

# 统计分析
statsmodels>=0.13.0

# 机器学习
scikit-learn>=1.1.0

# 自然语言处理
nltk>=3.7
jieba>=0.42.1

# 网络爬虫
beautifulsoup4>=4.11.0
lxml>=4.9.0
selenium>=4.10.0

# 数据库支持
sqlalchemy>=2.0.0
sqlite3

# 缓存支持
redis>=4.3.0

# 配置管理
configparser>=5.3.0
pyyaml>=6.0

# 时间处理
python-dateutil>=2.8.0

# JSON处理
ujson>=5.4.0

# 压缩支持
zipfile36>=0.1.3

# 系统信息
psutil>=5.9.0

# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0

# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# 安全
cryptography>=40.0.0
bcrypt>=4.0.0

# 监控和性能
prometheus-client>=0.16.0
memory-profiler>=0.60.0

# 多媒体处理
moviepy>=1.0.3
librosa>=0.10.0

# 文档生成
sphinx>=6.0.0
mkdocs>=1.4.0

# 其他工具
tqdm>=4.64.0
click>=8.0.0
rich>=13.0.0
typer>=0.9.0
