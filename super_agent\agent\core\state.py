"""
超级Agent状态管理模块
定义智能代理的状态结构和状态转换逻辑
"""

from typing import List, Dict, Any, Optional, TypedDict, Annotated
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field
from langgraph.graph import add_messages


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """任务类型枚举"""
    DATA_ANALYSIS = "data_analysis"
    KNOWLEDGE_SEARCH = "knowledge_search"
    WEB_SEARCH = "web_search"
    VISUALIZATION = "visualization"
    REPORT_GENERATION = "report_generation"
    HYBRID_ANALYSIS = "hybrid_analysis"


class AgentMode(str, Enum):
    """代理模式枚举"""
    ANALYSIS_ONLY = "analysis_only"
    SEARCH_ONLY = "search_only"
    HYBRID = "hybrid"
    KNOWLEDGE_BASE = "knowledge_base"


class Message(BaseModel):
    """消息模型"""
    role: str = Field(..., description="消息角色：user, assistant, system")
    content: str = Field(..., description="消息内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class Task(BaseModel):
    """任务模型"""
    id: str = Field(..., description="任务ID")
    type: TaskType = Field(..., description="任务类型")
    description: str = Field(..., description="任务描述")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    priority: int = Field(default=1, description="任务优先级")
    dependencies: List[str] = Field(default_factory=list, description="依赖任务ID列表")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="任务参数")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")


class KnowledgeItem(BaseModel):
    """知识项模型"""
    content: str = Field(..., description="知识内容")
    source: str = Field(..., description="知识来源")
    score: float = Field(..., description="相关性分数")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SearchResult(BaseModel):
    """搜索结果模型"""
    query: str = Field(..., description="搜索查询")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="搜索结果")
    source: str = Field(..., description="搜索来源")
    timestamp: datetime = Field(default_factory=datetime.now, description="搜索时间")


class AnalysisResult(BaseModel):
    """分析结果模型"""
    type: str = Field(..., description="分析类型")
    data: Dict[str, Any] = Field(..., description="分析数据")
    visualizations: List[str] = Field(default_factory=list, description="可视化文件路径")
    summary: str = Field(..., description="分析摘要")
    insights: List[str] = Field(default_factory=list, description="洞察发现")


class SuperAgentState(TypedDict):
    """超级Agent状态类型定义"""
    
    # 基础信息
    session_id: str
    user_id: Optional[str]
    mode: AgentMode
    
    # 消息历史
    messages: Annotated[List[Message], add_messages]
    
    # 当前任务信息
    current_task: Optional[Task]
    task_queue: List[Task]
    completed_tasks: List[Task]
    
    # 用户输入和意图
    user_message: str
    user_intent: Optional[str]
    extracted_parameters: Dict[str, Any]
    
    # 规划和执行
    plan: Optional[List[Task]]
    current_step: int
    execution_context: Dict[str, Any]
    
    # 知识库相关
    knowledge_results: List[KnowledgeItem]
    knowledge_query: Optional[str]
    
    # 网络搜索相关
    search_results: List[SearchResult]
    search_query: Optional[str]
    
    # 数据分析相关
    analysis_results: List[AnalysisResult]
    data_files: List[str]
    
    # 可视化相关
    visualizations: List[str]
    charts: List[Dict[str, Any]]
    
    # 报告生成
    report_sections: List[Dict[str, Any]]
    final_report: str
    
    # 系统状态
    observations: List[str]
    errors: List[str]
    warnings: List[str]
    
    # 性能监控
    start_time: datetime
    end_time: Optional[datetime]
    execution_time: Optional[float]
    
    # 配置和设置
    config: Dict[str, Any]
    preferences: Dict[str, Any]


class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self.state_history: List[SuperAgentState] = []
        self.current_state: Optional[SuperAgentState] = None
    
    def create_initial_state(
        self,
        session_id: str,
        user_message: str,
        mode: AgentMode = AgentMode.HYBRID,
        user_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> SuperAgentState:
        """创建初始状态"""
        
        initial_state: SuperAgentState = {
            # 基础信息
            "session_id": session_id,
            "user_id": user_id,
            "mode": mode,
            
            # 消息历史
            "messages": [Message(role="user", content=user_message)],
            
            # 任务信息
            "current_task": None,
            "task_queue": [],
            "completed_tasks": [],
            
            # 用户输入
            "user_message": user_message,
            "user_intent": None,
            "extracted_parameters": {},
            
            # 规划执行
            "plan": None,
            "current_step": 0,
            "execution_context": {},
            
            # 知识库
            "knowledge_results": [],
            "knowledge_query": None,
            
            # 网络搜索
            "search_results": [],
            "search_query": None,
            
            # 数据分析
            "analysis_results": [],
            "data_files": [],
            
            # 可视化
            "visualizations": [],
            "charts": [],
            
            # 报告
            "report_sections": [],
            "final_report": "",
            
            # 系统状态
            "observations": [],
            "errors": [],
            "warnings": [],
            
            # 性能监控
            "start_time": datetime.now(),
            "end_time": None,
            "execution_time": None,
            
            # 配置
            "config": config or {},
            "preferences": {}
        }
        
        self.current_state = initial_state
        return initial_state
    
    def update_state(self, updates: Dict[str, Any]) -> SuperAgentState:
        """更新状态"""
        if self.current_state is None:
            raise ValueError("No current state to update")
        
        # 保存历史状态
        self.state_history.append(self.current_state.copy())
        
        # 更新当前状态
        for key, value in updates.items():
            if key in self.current_state:
                self.current_state[key] = value
        
        return self.current_state
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """添加消息"""
        if self.current_state is None:
            raise ValueError("No current state")
        
        message = Message(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        self.current_state["messages"].append(message)
    
    def add_observation(self, observation: str) -> None:
        """添加观察"""
        if self.current_state is None:
            raise ValueError("No current state")
        
        self.current_state["observations"].append(observation)
    
    def add_error(self, error: str) -> None:
        """添加错误"""
        if self.current_state is None:
            raise ValueError("No current state")
        
        self.current_state["errors"].append(error)
    
    def get_current_state(self) -> Optional[SuperAgentState]:
        """获取当前状态"""
        return self.current_state
    
    def get_state_history(self) -> List[SuperAgentState]:
        """获取状态历史"""
        return self.state_history
    
    def rollback_state(self, steps: int = 1) -> SuperAgentState:
        """回滚状态"""
        if len(self.state_history) < steps:
            raise ValueError(f"Cannot rollback {steps} steps, only {len(self.state_history)} states in history")
        
        # 回滚到指定步数前的状态
        target_state = self.state_history[-(steps)]
        self.current_state = target_state.copy()
        
        # 移除回滚后的历史状态
        self.state_history = self.state_history[:-(steps)]
        
        return self.current_state
