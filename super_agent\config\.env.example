# 超级Agent配置文件示例
# 复制此文件为 .env 并填入实际配置值

# ================================
# LLM模型配置
# ================================

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-turbo-preview

# DeepSeek配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# Gemini配置
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-pro

# ================================
# 向量数据库配置
# ================================

# FAISS配置
FAISS_INDEX_PATH=data/knowledge_base/faiss_index
EMBEDDING_MODEL_PATH=models/bge-large-zh-v1.5
EMBEDDING_DEVICE=cpu
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# ================================
# 搜索引擎配置
# ================================

# SearxNG配置
SEARXNG_URL=http://localhost:8080
SEARXNG_LANGUAGE=zh-CN
SEARXNG_SAFESEARCH=0
SEARXNG_CATEGORIES=general

# ================================
# 数据库配置
# ================================

# SQLite配置
DATABASE_URL=sqlite:///data/super_agent.db

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# ================================
# 服务配置
# ================================

# 后端API配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
BACKEND_WORKERS=4

# 前端配置
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=7860
FRONTEND_SHARE=false

# ================================
# 文件存储配置
# ================================

# 上传文件配置
UPLOAD_DIR=data/uploads
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=pdf,docx,doc,txt,md,csv,xlsx,xls

# 输出文件配置
OUTPUT_DIR=data/outputs
CACHE_DIR=data/cache

# ================================
# 安全配置
# ================================

# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://localhost:7860

# ================================
# 日志配置
# ================================

# 日志级别
LOG_LEVEL=INFO
LOG_FILE=logs/super_agent.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# ================================
# 性能配置
# ================================

# 并发配置
MAX_WORKERS=4
ASYNC_TIMEOUT=300

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ================================
# 开发配置
# ================================

# 调试模式
DEBUG=false
DEVELOPMENT=false

# 热重载
AUTO_RELOAD=false

# ================================
# 监控配置
# ================================

# Prometheus配置
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# 健康检查
HEALTH_CHECK_INTERVAL=60
